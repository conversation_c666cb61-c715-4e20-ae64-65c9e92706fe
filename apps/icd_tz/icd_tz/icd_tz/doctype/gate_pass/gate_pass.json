{"actions": [], "allow_rename": 1, "autoname": "naming_series:naming_series", "creation": "2024-07-10 11:54:01.257045", "doctype": "DocType", "engine": "InnoDB", "field_order": ["gate_pass_details_section", "manifest", "vessel_name", "column_break_t0ure", "voyage_no", "ship_dc_date", "column_break_8s1tw", "company", "submitted_date", "submitted_time", "column_break_ecqj", "submitted_by", "expiry_date", "expiry_time", "action_for_missing_booking", "missing_booking_allowed_by", "container_details_section", "container_id", "m_bl_no", "h_bl_no", "sline", "column_break_bw5go", "container_no", "size", "seal_no", "container_status", "column_break_phfzs", "c_and_f_company", "clearing_agent", "tafer_id", "consignee", "column_break_1", "is_empty_container", "goods_description", "section_break_hn92e", "transporter", "naming_series", "amended_from", "column_break_5ivl7", "truck", "trailer", "column_break_5lxto", "driver", "license_no"], "fields": [{"fieldname": "gate_pass_details_section", "fieldtype": "Section Break", "label": "Gate Pass Details"}, {"fetch_from": "container_id.cargo_description", "fieldname": "goods_description", "fieldtype": "Small Text", "label": "Goods Description"}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Gate Pass", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fetch_from": "container_id.container_no", "fieldname": "container_no", "fieldtype": "Data", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "container No", "search_index": 1}, {"fieldname": "column_break_phfzs", "fieldtype": "Column Break"}, {"fieldname": "c_and_f_company", "fieldtype": "Link", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "C & F Company", "mandatory_depends_on": "eval: doc.is_empty_container == 0", "options": "Clearing and Forwarding Company", "search_index": 1}, {"default": "ICD-GP-.YYYY.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "ICD-GP-.YYYY.-", "read_only": 1}, {"fieldname": "clearing_agent", "fieldtype": "Link", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Clearing Agent", "mandatory_depends_on": "eval: doc.is_empty_container == 0", "options": "Clearing Agent", "search_index": 1}, {"fieldname": "consignee", "fieldtype": "Link", "in_preview": 1, "in_standard_filter": 1, "label": "Consignee", "options": "Consignee", "search_index": 1}, {"fieldname": "container_id", "fieldtype": "Link", "in_preview": 1, "in_standard_filter": 1, "label": "Container ID", "options": "Container", "read_only": 1, "reqd": 1, "search_index": 1}, {"fetch_from": "container_id.manifest", "fetch_if_empty": 1, "fieldname": "manifest", "fieldtype": "Link", "in_preview": 1, "in_standard_filter": 1, "label": "Manifest", "options": "Manifest", "read_only": 1, "search_index": 1}, {"fetch_from": "container_id.sline", "fieldname": "sline", "fieldtype": "Data", "label": "Sline"}, {"fetch_from": "manifest.vessel_name", "fieldname": "vessel_name", "fieldtype": "Data", "label": "Vessel Name"}, {"fetch_from": "manifest.voyage_no", "fieldname": "voyage_no", "fieldtype": "Data", "label": "Voyage No"}, {"fieldname": "column_break_t0ure", "fieldtype": "Column Break"}, {"fieldname": "column_break_5ivl7", "fieldtype": "Column Break"}, {"fieldname": "column_break_8s1tw", "fieldtype": "Column Break"}, {"fieldname": "submitted_by", "fieldtype": "Data", "label": "Submitted By", "read_only": 1}, {"fieldname": "container_details_section", "fieldtype": "Section Break", "label": "Container Details"}, {"fieldname": "column_break_bw5go", "fieldtype": "Column Break"}, {"fetch_from": "container_id.size", "fieldname": "size", "fieldtype": "Data", "label": "Size"}, {"fetch_from": "container_id.m_bl_no", "fieldname": "m_bl_no", "fieldtype": "Data", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "M BL No", "search_index": 1}, {"fetch_from": "container_id.seal_no_1", "fieldname": "seal_no", "fieldtype": "Data", "label": "Seal No"}, {"fieldname": "driver", "fieldtype": "Data", "label": "Driver"}, {"fieldname": "transporter", "fieldtype": "Data", "label": "Transporter"}, {"fieldname": "truck", "fieldtype": "Data", "label": "Truck"}, {"fetch_from": "manifest.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "section_break_hn92e", "fieldtype": "Section Break", "label": "Transport Details"}, {"fieldname": "column_break_5lxto", "fieldtype": "Column Break"}, {"fieldname": "trailer", "fieldtype": "Data", "label": "Trailer"}, {"fieldname": "license_no", "fieldtype": "Data", "label": "License No"}, {"fieldname": "submitted_date", "fieldtype": "Date", "label": "Submitted Date", "read_only": 1}, {"fieldname": "submitted_time", "fieldtype": "Time", "label": "Submitted Time", "read_only": 1}, {"fetch_from": "manifest.arrival_date", "fieldname": "ship_dc_date", "fieldtype": "Date", "label": "Ship D/C Date", "read_only": 1}, {"fetch_from": "container_id.h_bl_no", "fieldname": "h_bl_no", "fieldtype": "Data", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "H BL No", "read_only": 1, "search_index": 1}, {"fetch_from": "clearing_agent.tafer_id", "fieldname": "tafer_id", "fieldtype": "Data", "label": "Tafer ID", "search_index": 1}, {"fetch_from": "container_id.freight_indicator", "fieldname": "container_status", "fieldtype": "Data", "label": "Container Status", "search_index": 1}, {"default": "0", "fetch_from": "container_id.is_empty_container", "fieldname": "is_empty_container", "fieldtype": "Check", "label": "Is Empty Container", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_ecqj", "fieldtype": "Column Break"}, {"default": "Stop", "fieldname": "action_for_missing_booking", "fieldtype": "Select", "label": "Action for Missing Booking", "options": "\nStop\nAllow", "permlevel": 1, "search_index": 1}, {"fieldname": "missing_booking_allowed_by", "fieldtype": "Data", "label": "Missing Booking Allowed By", "read_only": 1}, {"default": "now", "description": "Gate Pass will expire on this date if container is not moved out", "fieldname": "expiry_date", "fieldtype": "Date", "label": "Expiry Date", "read_only": 1}, {"default": "now", "description": "Gate Pass will expire at this time if container is not moved out", "fieldname": "expiry_time", "fieldtype": "Time", "label": "Expiry Time", "read_only": 1}], "in_create": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-09-08 11:45:28.972756", "modified_by": "Administrator", "module": "Icd Tz", "name": "Gate Pass", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "show_preview_popup": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}