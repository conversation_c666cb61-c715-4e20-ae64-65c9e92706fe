# Server Script for Auto-Expiry of Gate Passes
# Script Name: Gate Pass - Auto Expiry Scheduler
# Script Type: Scheduler Event
# Event Frequency: Hourly

import frappe
from frappe.utils import now_datetime, get_datetime

def auto_expire_gate_passes():
    """Auto-expire and cancel Gate Passes that have exceeded their expiry time"""
    
    current_datetime = now_datetime()
    
    # Find submitted gate passes that have expired and are not confirmed
    expired_gate_passes = frappe.db.sql("""
        SELECT 
            name, container_no, expiry_date, expiry_time, 
            submitted_date, submitted_time, workflow_state
        FROM `tabGate Pass`
        WHERE 
            docstatus = 1 
            AND workflow_state != 'Gate Out Confirmed'
            AND expiry_date IS NOT NULL 
            AND expiry_time IS NOT NULL
            AND CONCAT(expiry_date, ' ', expiry_time) < %s
    """, (current_datetime,), as_dict=True)
    
    cancelled_count = 0
    
    for gp in expired_gate_passes:
        try:
            doc = frappe.get_doc("Gate Pass", gp.name)
            
            # Add a comment before cancelling
            expiry_datetime = get_datetime(f"{gp.expiry_date} {gp.expiry_time}")
            doc.add_comment("Comment", 
                f"Auto-cancelled due to expiry. Gate Pass expired on {expiry_datetime.strftime('%Y-%m-%d at %H:%M:%S')}. Container was not moved out within the agreed time.")
            
            # Cancel the document
            doc.cancel()
            
            cancelled_count += 1
            
            frappe.logger().info(f"Auto-cancelled expired Gate Pass: {gp.name} for container {gp.container_no}. Expired: {expiry_datetime}")
            
        except Exception as e:
            frappe.logger().error(f"Failed to auto-cancel Gate Pass {gp.name}: {str(e)}")
    
    if cancelled_count > 0:
        frappe.logger().info(f"Auto-expiry process completed. Cancelled {cancelled_count} expired Gate Passes.")
        
        # Optional: Send notification to administrators
        frappe.publish_realtime(
            event="msgprint",
            message=f"Auto-expired {cancelled_count} Gate Pass(es) due to timeout.",
            user="Administrator"
        )

# Execute the function
auto_expire_gate_passes()
