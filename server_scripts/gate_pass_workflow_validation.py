# Server Script for Gate Pass Workflow Payment Validation
# Script Name: Gate Pass - Workflow Payment Validation
# Script Type: DocType Event
# Reference DocType: Gate Pass
# Event: Before Save

# Validate payments when transitioning to Gate Out Confirmed
# This ensures the same payment validation as submission

if doc.workflow_state == "Gate Out Confirmed":
    # Check if this is a transition TO Gate Out Confirmed
    if doc._doc_before_save and doc._doc_before_save.workflow_state != "Gate Out Confirmed":
        
        # Skip validation for empty containers
        if doc.is_empty_container == 1:
            return
        
        # Use the existing payment validation logic
        service_msg = ""
        
        # Validate container charges
        container_info = frappe.db.get_value(
            "Container",
            doc.container_id,
            ["has_removal_charges", "r_sales_invoice", "has_corridor_levy_charges", "c_sales_invoice", "days_to_be_billed"],
            as_dict=True
        )
        
        if container_info and container_info.days_to_be_billed > 0:
            service_msg += f"<li>Storage Charges: <b>{container_info.days_to_be_billed} Days</b></li>"
        
        if container_info and container_info.has_removal_charges == "Yes" and not container_info.r_sales_invoice:
            service_msg += "<li>Removal Charges</li>"
        
        if container_info and container_info.has_corridor_levy_charges == "Yes" and not container_info.c_sales_invoice:
            service_msg += "<li>Corridor Levy Charges</li>"
        
        # Validate in yard booking charges
        booking_info = frappe.db.get_all(
            "In Yard Container Booking",
            {
                "container_id": doc.container_id,
                "docstatus": ["!=", 2],  # Exclude cancelled bookings
            },
            ["has_stripping_charges", "s_sales_invoice", "has_custom_verification_charges", "cv_sales_invoice"],
        )
        
        for row in booking_info:
            if row.has_stripping_charges == "Yes" and not row.s_sales_invoice:
                service_msg += "<li>Stripping Charges</li>"
            
            if row.has_custom_verification_charges == "Yes" and not row.cv_sales_invoice:
                service_msg += "<li>Custom Verification Charges</li>"
        
        # Validate reception charges
        container_reception = frappe.db.get_value("Container", doc.container_id, "container_reception")
        if container_reception:
            reception_info = frappe.db.get_value(
                "Container Reception",
                container_reception,
                ["cargo_type", "has_transport_charges", "t_sales_invoice", "has_shore_handling_charges", "s_sales_invoice"],
                as_dict=True
            )
            
            if (reception_info and reception_info.has_transport_charges == "Yes" 
                and not reception_info.t_sales_invoice 
                and reception_info.cargo_type != "Transit"):
                service_msg += "<li>Transport Charges</li>"
            
            if (reception_info and reception_info.has_shore_handling_charges == "Yes" 
                and not reception_info.s_sales_invoice):
                service_msg += "<li>Shore Handling Charges</li>"
        
        # Validate inspection charges
        inspection_info = frappe.db.get_all(
            "Container Inspection",
            {"container_id": doc.container_id},
            pluck="name"
        )
        
        for inspection in inspection_info:
            inspection_doc = frappe.get_doc("Container Inspection", inspection)
            
            for d in inspection_doc.get("services"):
                if "off" in str(d.get("service")).lower() and not d.get("sales_invoice"):
                    service_msg += f"<li>{d.get('service')}</li>"
                
                if "status" in str(d.get("service")).lower() and not d.get("sales_invoice"):
                    service_msg += f"<li>{d.get('service')}</li>"
        
        # If there are pending payments, prevent the transition
        if service_msg:
            msg = "<h4 class='text-center'>Cannot Confirm Gate Out - Pending Payments:</h4><hr>Payment is pending for the following services <ul> " + service_msg + " </ul>"
            frappe.throw(str(msg))
