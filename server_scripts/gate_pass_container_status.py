# Server Script for Gate Pass Container Status Management
# Script Name: Gate Pass - Container Status Management
# Script Type: DocType Event
# Reference DocType: Gate Pass
# Event: After Save

# Handle container status based on Gate Pass workflow state changes

if doc.container_id:
    container = frappe.get_doc("Container", doc.container_id)
    
    # When Gate Pass is confirmed as Gate Out
    if doc.workflow_state == "Gate Out Confirmed":
        container.status = "Delivered"
        container.save(ignore_permissions=True)
        frappe.logger().info(f"Container {container.container_no} status updated to 'Delivered' via Gate Pass {doc.name}")
    
    # When Gate Pass is cancelled, revert container status to 'At Gatepass'
    elif doc.docstatus == 2:  # Cancelled
        container.status = "At Gatepass"
        container.save(ignore_permissions=True)
        frappe.logger().info(f"Container {container.container_no} status reverted to 'At Gatepass' due to cancelled Gate Pass {doc.name}")
