# Gate Pass Expiry and Workflow Implementation

## Overview
This implementation adds comprehensive gate pass expiry management and workflow functionality for the ICD TZ system.

## Features Implemented

### 1. ✅ Gate Pass Expiry Hours Configuration
- **Location**: ICD TZ Settings doctype already has `expiry_hours` field
- **Purpose**: Stores the number of hours before gate pass expires
- **Default**: 72 hours if not configured

### 2. ✅ Expiry Date/Time Display
- **Fields Added**: `expiry_date` and `expiry_time` in Gate Pass doctype
- **Auto-calculated**: When gate pass is submitted
- **Display**: Shows when the gate pass will expire

### 3. ✅ Auto-Expiry Background Job
- **File**: `server_scripts/gate_pass_auto_expiry.py`
- **Frequency**: Hourly scheduler
- **Function**: Auto-cancels expired gate passes that haven't been confirmed

### 4. ✅ Container Status Management
- **On Gate Out Confirmed**: Container status → "Delivered"
- **On Gate Pass Cancelled**: Container status → "At Gatepass"

### 5. ✅ Gate Person Action Buttons
- **Valid Gate Pass**: Shows "Confirm Gate Out" button
- **Expired Gate Pass**: Shows "Cancel Expired Gate Pass" button
- **Visual Indicators**: Dashboard comments showing expiry status

### 6. ✅ Payment Validation
- **Workflow Transition**: Validates payments when transitioning to "Gate Out Confirmed"
- **Same Logic**: Uses identical validation as gate pass submission

## Files Modified

### Core Files
1. `apps/icd_tz/icd_tz/icd_tz/doctype/gate_pass/gate_pass.py`
   - Added `set_expiry_datetime()` method
   - Updated container status logic
   - Added `gate_out_confirm()` method

2. `apps/icd_tz/icd_tz/icd_tz/doctype/gate_pass/gate_pass.json`
   - Added `expiry_date` and `expiry_time` fields

3. `apps/icd_tz/icd_tz/icd_tz/doctype/gate_pass/gate_pass.js`
   - Added Gate Person action buttons
   - Added expiry validation logic
   - Added visual indicators

### Server Scripts (To be created in Frappe Desk)
1. **Gate Pass - Workflow Payment Validation**
   - Type: DocType Event (Before Save)
   - File: `server_scripts/gate_pass_workflow_validation.py`

2. **Gate Pass - Container Status Management**
   - Type: DocType Event (After Save)
   - File: `server_scripts/gate_pass_container_status.py`

3. **Gate Pass - Auto Expiry Scheduler**
   - Type: Scheduler Event (Hourly)
   - File: `server_scripts/gate_pass_auto_expiry.py`

## Setup Instructions

### 1. Configure ICD TZ Settings
1. Go to **ICD TZ Settings**
2. Set **Gate Pass Expiry Notice Hours** (e.g., 72 for 3 days)

### 2. Create Server Scripts in Frappe Desk
Navigate to **Setup > Automation > Server Script** and create:

#### Script 1: Payment Validation
- **Script Name**: `Gate Pass - Workflow Payment Validation`
- **Script Type**: `DocType Event`
- **Reference DocType**: `Gate Pass`
- **Event**: `Before Save`
- **Code**: Copy from `server_scripts/gate_pass_workflow_validation.py`

#### Script 2: Container Status Management
- **Script Name**: `Gate Pass - Container Status Management`
- **Script Type**: `DocType Event`
- **Reference DocType**: `Gate Pass`
- **Event**: `After Save`
- **Code**: Copy from `server_scripts/gate_pass_container_status.py`

#### Script 3: Auto-Expiry Scheduler
- **Script Name**: `Gate Pass - Auto Expiry Scheduler`
- **Script Type**: `Scheduler Event`
- **Event Frequency**: `Hourly`
- **Code**: Copy from `server_scripts/gate_pass_auto_expiry.py`

### 3. Workflow Configuration
Ensure your Gate Pass workflow includes:
- **States**: Draft, Submitted, Gate Out Confirmed, Cancelled
- **Transitions**: 
  - Submitted → Gate Out Confirmed (Gate Person role)
  - Submitted → Cancelled (System Manager)

## How It Works

### Gate Pass Submission
1. When submitted, expiry date/time is automatically calculated
2. Based on ICD TZ Settings expiry hours
3. Fields are populated and displayed to user

### Gate Person Actions
1. **Valid Gate Pass**: 
   - Shows "Confirm Gate Out" button
   - Validates payments before confirming
   - Updates container status to "Delivered"

2. **Expired Gate Pass**:
   - Shows "Cancel Expired Gate Pass" button
   - Updates container status to "At Gatepass"
   - Adds cancellation comment

### Auto-Expiry Process
1. Runs every hour
2. Finds gate passes past expiry time
3. Auto-cancels with explanatory comment
4. Updates container status to "At Gatepass"

### Visual Indicators
- **Valid**: Blue comment showing expiry date/time
- **Expired**: Red warning comment
- **Buttons**: Color-coded (green for confirm, red for cancel)

## Benefits
- ✅ Automated expiry management
- ✅ Payment validation consistency
- ✅ Clear visual indicators
- ✅ Proper container status tracking
- ✅ Audit trail with comments
- ✅ Role-based access control
- ✅ No manual intervention required

## Testing
1. Create a test gate pass
2. Set short expiry hours in settings (e.g., 1 hour)
3. Submit the gate pass
4. Verify expiry date/time is set
5. Wait for expiry and check auto-cancellation
6. Test Gate Person buttons functionality
