2025-08-18 13:01:17,793 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-18 13:01:17,799 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 13:01:17,800 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 13:01:17,802 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 13:01:17,806 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 13:01:17,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 13:01:17,813 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-18 13:01:17,818 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-18 13:01:17,819 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 13:01:17,824 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 13:01:17,825 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-18 13:01:17,828 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 13:01:17,830 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 13:01:17,840 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-18 13:01:17,841 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 13:01:17,844 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 13:01:17,847 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 13:01:17,848 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-18 13:01:17,850 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 13:01:17,852 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-18 13:01:17,854 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 13:01:17,857 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 13:01:17,863 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 14:01:35,010 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 14:01:35,026 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 14:01:35,054 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 14:01:35,058 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 14:01:35,071 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 14:01:35,079 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 14:01:35,098 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 14:01:35,115 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 14:01:35,118 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 14:01:35,123 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 14:01:35,127 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 14:01:35,141 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 14:01:35,153 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 14:02:35,371 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 14:02:35,376 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 14:02:35,383 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 14:02:35,388 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 14:02:35,400 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 14:02:35,403 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 14:02:35,406 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 14:02:35,411 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 14:02:35,412 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 14:02:35,421 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 14:02:35,440 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 14:02:35,453 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 14:02:35,457 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 15:01:52,365 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 15:01:52,376 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 15:01:52,381 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 15:01:52,393 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 15:01:52,394 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 15:01:52,403 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 15:01:52,414 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 15:01:52,416 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 15:01:52,419 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 15:01:52,429 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 15:01:52,448 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 15:01:52,452 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 15:01:52,471 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 16:01:12,645 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 16:01:12,695 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-18 16:01:12,701 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-18 16:01:12,709 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-18 16:01:12,711 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 16:01:12,713 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 16:01:12,714 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-18 16:01:12,719 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-18 16:01:12,721 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 16:01:12,723 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 16:01:12,727 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-18 16:01:12,729 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 16:01:12,730 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 16:01:12,733 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 16:01:12,735 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-18 16:01:12,737 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 16:01:12,745 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-18 16:01:12,749 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-18 16:01:12,750 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 16:01:12,757 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-18 16:01:12,759 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-18 16:01:12,766 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 16:01:12,770 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 16:01:12,773 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 16:01:12,775 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-18 16:01:12,776 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 16:01:12,786 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-18 16:01:12,793 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 16:01:12,795 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 16:01:12,808 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 16:01:12,811 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 16:01:12,813 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 16:01:12,815 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 16:01:12,817 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-18 16:01:12,822 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 16:01:12,823 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-18 16:01:12,827 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-18 16:01:12,829 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-18 16:01:12,832 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 16:01:12,839 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-18 16:02:12,862 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 16:02:12,865 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 16:02:12,870 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 16:02:12,879 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 16:02:12,888 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 16:02:12,907 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 16:02:12,908 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 16:02:12,929 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 16:02:12,944 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 16:02:12,949 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 16:02:12,959 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 16:02:12,969 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 16:02:12,977 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 17:01:32,558 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 17:01:32,561 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-18 17:01:32,565 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-18 17:01:32,568 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 17:01:32,570 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 17:01:32,573 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-18 17:01:32,576 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 17:01:32,578 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-18 17:01:32,581 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 17:01:32,583 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-18 17:01:32,585 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 17:01:32,587 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 17:01:32,589 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 17:01:32,594 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-18 17:01:32,601 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 17:01:32,602 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-18 17:01:32,610 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-18 17:01:32,615 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 17:01:32,616 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-18 17:01:32,621 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 17:01:32,622 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 17:01:32,625 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-18 17:01:32,633 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-18 17:01:32,641 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-18 17:01:32,643 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 17:01:32,645 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 17:01:32,651 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-18 17:01:32,653 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 17:01:32,658 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 17:01:32,663 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 17:01:32,669 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 17:01:32,672 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-18 17:01:32,678 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 17:01:32,683 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-18 17:01:32,685 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-18 17:01:32,690 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 17:01:32,696 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 17:01:32,699 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-18 17:01:32,714 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 18:01:53,325 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-18 18:01:53,331 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-18 18:01:53,334 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 18:01:53,337 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-18 18:01:53,364 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 18:01:53,368 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-18 18:01:53,370 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-18 18:01:53,377 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-18 18:01:53,384 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-18 18:01:53,386 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-18 18:01:53,393 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-18 18:01:53,395 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-18 18:01:53,409 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 18:01:53,412 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 18:01:53,415 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-08-18 18:01:53,426 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-18 18:01:53,428 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-18 18:01:53,431 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-18 18:01:53,434 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 18:01:53,440 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 18:01:53,446 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 18:01:53,448 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-18 18:01:53,451 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 18:01:53,457 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 18:01:53,459 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-18 18:01:53,461 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 18:01:53,468 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-18 18:01:53,472 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 18:01:53,478 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 18:01:53,481 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 18:01:53,488 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 18:01:53,498 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 18:01:53,505 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 18:01:53,510 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 18:01:53,518 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 18:01:53,521 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-18 18:01:53,524 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 18:01:53,528 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 18:01:53,531 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 18:01:53,534 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-18 18:01:53,537 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 18:02:53,845 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 18:02:53,853 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-18 18:02:53,855 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 18:02:53,857 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 18:02:53,859 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 18:02:53,866 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 18:02:53,869 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-18 18:02:53,871 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-18 18:02:53,883 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-18 18:02:53,887 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 18:02:53,890 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 18:02:53,892 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 18:02:53,895 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-18 18:02:53,903 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 18:02:53,905 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 18:02:53,911 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-18 18:02:53,912 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 18:02:53,917 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 18:02:53,920 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-18 18:02:53,923 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 18:02:53,932 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 18:02:53,936 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-18 18:02:53,939 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-18 18:02:53,942 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-18 18:02:53,946 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 18:02:53,948 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-08-18 18:02:53,964 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 18:02:53,968 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-18 18:02:53,978 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-18 18:02:53,983 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-18 18:02:53,986 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 18:02:53,989 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 18:02:54,007 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-18 18:02:54,009 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 18:02:54,016 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 18:02:54,021 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 18:02:54,023 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-18 18:02:54,027 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-18 18:02:54,029 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-18 18:02:54,035 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-18 18:02:54,041 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 18:03:55,662 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-18 18:03:55,689 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-18 18:03:55,693 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-18 18:03:55,696 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-18 18:03:55,699 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-18 18:03:55,702 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-18 18:03:55,727 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-18 18:03:55,738 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-18 18:03:55,750 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-18 18:03:55,754 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-18 18:03:55,755 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-18 18:03:55,764 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-18 18:03:55,807 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-18 18:17:00,182 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-18 18:17:00,187 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-18 18:17:00,207 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-18 18:17:00,231 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-18 18:17:00,237 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-18 18:17:00,261 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-18 18:17:00,278 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-18 18:17:00,280 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-18 18:17:00,304 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-18 18:17:00,306 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-18 18:17:00,308 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-18 18:17:00,317 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-18 18:17:00,323 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-18 18:17:00,325 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-18 18:17:00,339 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-19 17:19:32,925 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:19:32,931 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:19:32,933 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:19:32,946 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:19:32,948 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:19:32,953 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:19:32,957 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:19:32,960 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:19:32,964 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:19:32,970 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:19:32,972 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:19:32,974 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:19:32,976 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:19:32,986 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:19:32,988 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:19:33,009 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:19:33,012 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:19:33,015 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:19:33,018 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:19:33,020 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:19:33,025 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:19:33,028 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:19:33,035 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:19:33,043 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:19:33,046 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:19:33,048 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:19:33,051 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:19:33,054 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:19:33,058 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:19:33,060 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:19:33,075 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:19:33,078 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:22:23,904 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-19 17:22:23,906 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:22:23,914 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:22:23,917 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:22:23,919 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:22:23,921 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:22:23,926 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-19 17:22:23,928 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:22:23,930 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:22:23,940 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:22:23,942 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:22:23,946 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:22:23,949 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:22:23,951 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:22:23,953 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:22:23,954 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:22:23,959 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:22:23,965 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:22:23,966 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:22:23,968 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:22:23,972 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:22:23,975 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:22:23,979 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:22:23,981 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:22:23,996 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:22:24,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:22:24,006 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:22:24,009 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:22:24,014 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:22:24,018 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:22:24,023 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:22:24,029 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:22:24,032 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:22:24,035 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:22:24,037 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:22:24,040 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:22:24,046 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:22:24,051 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:22:24,053 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:22:24,070 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:23:24,331 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:23:24,333 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:23:24,339 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:23:24,340 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:23:24,342 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:23:24,347 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:23:24,350 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:23:24,359 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:23:24,366 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:23:24,369 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:23:24,371 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:23:24,375 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:23:24,380 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:23:24,385 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:23:24,389 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-19 17:23:24,392 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:23:24,396 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:23:24,399 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:23:24,401 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:23:24,403 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:23:24,406 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:23:24,408 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:23:24,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:23:24,421 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:23:24,423 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:23:24,431 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:23:24,438 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:23:24,444 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:23:24,448 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:23:24,451 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:23:24,454 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-19 17:23:24,466 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:23:24,468 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:23:24,477 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:23:24,483 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:23:24,487 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:23:24,489 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:23:24,500 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:23:24,502 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:23:24,509 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:24:24,755 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:24:24,761 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:24:24,762 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:24:24,770 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:24:24,773 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:24:24,780 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:24:24,784 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:24:24,793 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:24:24,795 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:24:24,797 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:24:24,799 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:24:24,801 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:24:24,802 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:24:24,806 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:24:24,809 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:24:24,818 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:24:24,824 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:24:24,827 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:24:24,828 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:24:24,829 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:24:24,837 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:24:24,841 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:24:24,844 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:24:24,846 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:24:24,848 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:24:24,853 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:24:24,855 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:24:24,856 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:24:24,858 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:24:24,860 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:24:24,863 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:24:24,867 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:25:24,914 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:25:24,921 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:25:24,923 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:25:24,925 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:25:24,927 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:25:24,930 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:25:24,932 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:25:24,934 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:25:24,936 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:25:24,940 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:25:24,943 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:25:24,946 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:25:24,949 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:25:24,951 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:25:24,952 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:25:24,957 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:25:24,962 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:25:24,964 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:25:24,969 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:25:24,971 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:25:24,972 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:25:24,977 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:25:24,978 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:25:24,983 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:25:24,986 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:25:24,987 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:25:24,990 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:25:24,992 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:25:24,997 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:25:25,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:25:25,007 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:25:25,012 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:25:25,014 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:25:25,022 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:25:25,030 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:25:25,033 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:25:25,039 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:25:25,042 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:26:25,237 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:26:25,244 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:26:25,248 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:26:25,251 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:26:25,253 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:26:25,258 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:26:25,261 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:26:25,264 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:26:25,266 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:26:25,270 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:26:25,273 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:26:25,276 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:26:25,284 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:26:25,286 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:26:25,295 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:26:25,299 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:26:25,302 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:26:25,303 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:26:25,305 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:26:25,309 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:26:25,315 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:26:25,323 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:26:25,326 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:26:25,347 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:26:25,352 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:26:25,364 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:26:25,368 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:26:25,370 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:26:25,372 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:26:25,376 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:26:25,378 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:26:25,383 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:26:25,395 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:26:25,397 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:26:25,399 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:26:25,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:26:25,404 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:26:25,409 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:27:25,652 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:27:25,660 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:27:25,663 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:27:25,669 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:27:25,671 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:27:25,672 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:27:25,674 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:27:25,676 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:27:25,684 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:27:25,686 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:27:25,689 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:27:25,696 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:27:25,702 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:27:25,706 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:27:25,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:27:25,710 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:27:25,711 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:27:25,713 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:27:25,716 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:27:25,722 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:27:25,725 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:27:25,727 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:27:25,730 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:27:25,731 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:27:25,733 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:27:25,736 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:27:25,738 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:27:25,739 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:27:25,751 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:27:25,757 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:27:25,759 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:27:25,761 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:27:25,764 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:27:25,766 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:27:25,769 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:27:25,774 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:27:25,777 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:27:25,779 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:28:26,244 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:28:26,251 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:28:26,253 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:28:26,256 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:28:26,259 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:28:26,266 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:28:26,269 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:28:26,271 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:28:26,272 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:28:26,275 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:28:26,277 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:28:26,287 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:28:26,297 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:28:26,303 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:28:26,305 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:28:26,308 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:28:26,314 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:28:26,318 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:28:26,325 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:28:26,328 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:28:26,330 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:28:26,342 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:28:26,346 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:28:26,355 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:28:26,367 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:28:26,372 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:28:26,379 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:28:26,382 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:28:26,389 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:28:26,392 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:28:26,400 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:28:26,401 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:28:26,403 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:28:26,406 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:28:26,413 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:28:26,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:28:26,419 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:28:26,422 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:29:26,449 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:29:26,452 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:29:26,458 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-19 17:29:26,472 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:29:26,474 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:29:26,478 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-19 17:29:26,486 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-19 17:29:26,489 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-19 17:29:26,493 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-19 17:29:26,496 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:29:26,504 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:29:26,511 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-19 17:29:26,512 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-19 17:29:26,514 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:29:26,518 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-19 17:29:26,523 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:29:26,525 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-19 17:29:26,526 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-19 17:29:26,528 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-19 17:29:26,534 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:29:26,536 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:29:26,540 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-19 17:29:26,542 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:29:26,545 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-19 17:29:26,547 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:29:26,549 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-19 17:29:26,553 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:29:26,556 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:29:26,558 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-19 17:29:26,560 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:29:26,571 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-19 17:29:26,578 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:29:26,580 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-19 17:29:26,584 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:29:26,591 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-19 17:29:26,595 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:29:26,602 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:29:26,605 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-19 17:30:27,012 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-19 17:30:27,023 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-19 17:30:27,025 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-19 17:30:27,027 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-19 17:30:27,033 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-19 17:30:27,035 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-19 17:30:27,038 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-19 17:30:27,045 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-19 17:30:27,046 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-19 17:30:27,048 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-19 17:30:27,050 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-19 17:30:27,059 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-19 17:30:27,067 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-19 17:30:27,074 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-19 17:30:27,076 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-19 17:30:27,083 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-19 17:30:27,086 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-19 17:30:27,090 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-19 17:30:27,096 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
